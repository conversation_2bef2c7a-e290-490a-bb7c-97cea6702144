#pragma once
#include <mysql.h>
#include <string>
#include <memory>
#include <unordered_map>
#include <mutex>

// Forward declarations
class MySQLCommand;
class RecordSet;

// 심플한 MySQL 연결 클래스 (논블로킹 지원)
class NSMySQLConnection
{
public:
    NSMySQLConnection();
    ~NSMySQLConnection();

    // 연결 관리
    bool Connect(const char* host, int port, const char* user, 
                 const char* password, const char* database);
    void Disconnect();
    bool Reconnect();
    bool IsConnected() const;

    // 프로시저 실행
    bool ExecuteProcedure(const std::string& procName, MySQLCommand* command);
    
    // 쿼리 실행
    bool ExecuteQuery(const std::string& query);
    
    // 결과 가져오기
    std::unique_ptr<RecordSet> GetRecordSet();
    
    // 트랜잭션
    bool BeginTransaction();
    bool CommitTransaction();
    bool RollbackTransaction();

    // 논블로킹 실행 지원
    bool ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command);
    bool PollAsyncResult();

    // MySQL 핸들 접근
    MYSQL* GetHandle() { return m_mysql; }

    // 에러 정보
    const char* GetLastError() const;
    int GetLastErrorCode() const;

private:
    // 프로시저 메타데이터 캐싱
    struct ProcedureMetadata
    {
        struct Parameter
        {
            std::string name;
            enum_field_types type;
            bool isOutput;
        };
        std::vector<Parameter> parameters;
        bool hasResultSet;
    };
    
    bool LoadProcedureMetadata(const std::string& procName);
    ProcedureMetadata* GetProcedureMetadata(const std::string& procName);

private:
    MYSQL* m_mysql = nullptr;
    MYSQL_STMT* m_stmt = nullptr;
    MYSQL_RES* m_result = nullptr;
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    // 프로시저 메타데이터 캐시
    std::unordered_map<std::string, ProcedureMetadata> m_metadataCache;
    std::mutex m_metadataMutex;
    
    // PreparedStatement 캐시 (LRU)
    struct StmtCacheEntry
    {
        MYSQL_STMT* stmt;
        std::string query;
        uint64_t lastUsed;
    };
    std::unordered_map<std::string, StmtCacheEntry> m_stmtCache;
    static constexpr size_t MAX_STMT_CACHE_SIZE = 100;
    
    // 비동기 실행 상태
    bool m_asyncExecuting = false;
    int m_asyncStatus = 0;
};