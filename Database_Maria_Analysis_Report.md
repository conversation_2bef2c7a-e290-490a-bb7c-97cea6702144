# Database_Maria 라이브러리 문제점 분석 보고서

## 📋 개요

**분석 대상**: Database_Maria 라이브러리  
**환경**: 게임서버 (MMO, 윈도우, 멀티 프로세스, C++, MariaDB/MySQL)  
**분석 일자**: 2025-07-22  
**분석 범위**: 메모리 관리, 스레드 안전성, MySQL 연결, 에러 처리, 의존성, 성능

---

## 🚨 심각도별 문제점 분류

### 🔴 Critical (즉시 수정 필요)

#### 1. 연결 풀 메모리 안전성 위반
**파일**: `ConnectionPool/NSMySQLConnectionPool.cpp`
```cpp
// 문제 코드
if (tc.connection && tc.connection->IsConnected())
{
    tc.lastUsed = std::chrono::steady_clock::now();
    tc.queryCount++;
    tc.allocated = true;
    return std::move(tc.connection);  // 임시 대여 - 위험!
}
```
**문제점**:
- `std::move` 후 `tc.connection`이 nullptr이 되지만 `allocated = true`로 설정
- 여러 스레드가 동시에 같은 threadIndex에 접근 가능
- 연결 상태 불일치로 인한 크래시 위험

#### 2. MySQL C API 부적절한 사용
**파일**: `Connection/NSMySQLConnection.cpp`
```cpp
// 문제 코드
mysql_bool_compat reconnect = 1;
mysql_options(m_mysql, MYSQL_OPT_RECONNECT, &reconnect);
```
**문제점**:
- `MYSQL_OPT_RECONNECT`는 MySQL 5.7+에서 deprecated
- 스레드 안전하지 않음
- 연결 실패 시 적절한 정리 없음

#### 3. PreparedStatement 메모리 누수
**파일**: `Connection/NSMySQLConnection.h`
```cpp
// 문제 코드
std::unordered_map<std::string, StmtCacheEntry> m_stmtCache;
static constexpr size_t MAX_STMT_CACHE_SIZE = 100;
```
**문제점**:
- LRU 구현 부재로 무한 증가 가능
- 캐시에서 제거 시 `mysql_stmt_close()` 호출 안 함
- 스레드 안전성 부족

### 🟡 High (성능 및 안정성 영향)

#### 4. 스레드 안전성 위반
**파일**: `NSDataBaseManager.cpp`
```cpp
// 문제 코드
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    int threadIndex = GetExecutorByShardKey(cid);
    // 해당 스레드의 로컬 데이터에서 시퀀스 증가 (락 불필요) - 잘못된 가정!
    return ++m_threadData[threadIndex].sequenceByCid[cid];
}
```
**문제점**:
- 여러 스레드가 같은 threadIndex를 가질 수 있음
- 다른 CID가 같은 스레드에 매핑될 때 시퀀스 충돌

#### 5. 재시도 정책 안전성 부족
**파일**: `RetryPolicy.h`
```cpp
// 문제 코드
double jitter = 0.5 + (static_cast<double>(rand()) / RAND_MAX);
```
**문제점**:
- 스레드 안전하지 않은 `rand()` 함수 사용
- `srand()` 초기화 부재로 항상 같은 패턴

#### 6. 메모리 관리 비효율성
**파일**: `Memory/MemoryPool.h`
```cpp
// 문제 코드
void Return(T* obj)
{
    m_pool.push(std::unique_ptr<T>(obj));  // 이중 삭제 위험!
}
```
**문제점**:
- 이미 관리되던 포인터를 다시 unique_ptr로 감쌈
- 모든 반환 시 뮤텍스 락으로 성능 저하

### 🟢 Medium (개선 권장)

#### 7. 성능 병목점
- 고정된 12개 연결 풀 (GameDB 0-9 + CommonDB + LogDB)
- 최대 32개 스레드 제한
- 비효율적인 1ms 폴링 방식 비동기 처리

#### 8. 에러 처리 시스템 불완전
- 에러 코드 정의 불일치
- 로깅 실패 시 정보 손실
- 백업 로깅 메커니즘 부재

#### 9. 빌드 시스템 복잡성
- 조건부 컴파일로 인한 디버그/릴리즈 빌드 차이
- mimalloc과 CRT 디버그 힙 충돌 가능성

---

## 🛠️ 권장 해결 방안

### 즉시 조치 사항 (Critical)

1. **연결 풀 안전성 수정**
   ```cpp
   // 권장 해결책
   class ConnectionGuard {
       NSMySQLConnectionPool* pool;
       std::unique_ptr<NSMySQLConnection> conn;
   public:
       ConnectionGuard(NSMySQLConnectionPool* p, int threadIndex) 
           : pool(p), conn(p->GetConnection(threadIndex)) {}
       ~ConnectionGuard() { if(conn) pool->ReturnConnection(std::move(conn)); }
       NSMySQLConnection* get() { return conn.get(); }
   };
   ```

2. **MySQL API 업데이트**
   - MySQL Connector/C++ 8.0+ 사용 검토
   - 수동 재연결 로직 구현
   - 연결 상태 체크 강화

3. **스레드 안전 난수 생성**
   ```cpp
   // 권장 해결책
   thread_local std::mt19937 generator(std::random_device{}());
   std::uniform_real_distribution<double> distribution(0.5, 1.5);
   double jitter = distribution(generator);
   ```

### 중장기 개선 사항 (High)

1. **메모리 관리 통합**
   - mimalloc 전역 적용
   - 커스텀 할당자 구현
   - 버퍼 풀링 시스템 구축

2. **비동기 처리 개선**
   - MySQL 8.0+ 비동기 API 활용
   - 이벤트 기반 처리로 폴링 제거
   - 코루틴 도입 검토

3. **모니터링 시스템**
   - 연결 풀 상태 모니터링
   - 메모리 사용량 추적
   - 성능 메트릭 수집

### 아키텍처 개선 (Medium)

1. **설정 기반 확장성**
   - 런타임 스레드/연결 수 조정
   - 동적 샤드 관리
   - 로드 밸런싱 구현

2. **장애 복구 메커니즘**
   - Circuit Breaker 패턴
   - 자동 재연결 로직
   - Graceful Degradation

---

## 📊 영향도 분석

| 문제 유형 | 영향도 | 발생 확률 | 우선순위 |
|-----------|--------|-----------|----------|
| 연결 풀 메모리 안전성 | 높음 | 높음 | 1 |
| MySQL API 부적절 사용 | 높음 | 중간 | 2 |
| 스레드 안전성 위반 | 높음 | 중간 | 3 |
| 메모리 누수 | 중간 | 높음 | 4 |
| 성능 병목 | 중간 | 중간 | 5 |

---

## 🎯 결론 및 권고사항

Database_Maria 라이브러리는 **심각한 메모리 안전성 및 스레드 안전성 문제**를 포함하고 있어 즉시 수정이 필요합니다. 특히 연결 풀 관리와 MySQL API 사용 부분은 게임서버 안정성에 직접적인 영향을 미칠 수 있습니다.

**단계별 개선 로드맵**:
1. **1단계 (즉시)**: Critical 문제 수정
2. **2단계 (1개월)**: High 우선순위 문제 해결
3. **3단계 (3개월)**: 아키텍처 개선 및 성능 최적화

이러한 개선을 통해 게임서버의 안정성과 성능을 크게 향상시킬 수 있을 것으로 예상됩니다.
