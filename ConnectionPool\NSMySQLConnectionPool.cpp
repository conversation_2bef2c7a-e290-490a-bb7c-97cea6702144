#include "../stdafx.h"
#include "NSMySQLConnectionPool.h"
#include "../Connection/NSMySQLConnection.h"
#include <chrono>

// thread_local 제거 - 스레드별 전용 커넥션으로 대체

NSMySQLConnectionPool::NSMySQLConnectionPool()
{
}

NSMySQLConnectionPool::~NSMySQLConnectionPool()
{
    Finalize();
}

bool NSMySQLConnectionPool::Initialize(int databaseType, int shardId)
{
    if (m_initialized)
        return true;

    m_databaseType = databaseType;
    m_shardId = shardId;

    // 연결 정보 로드
    if (!LoadConnectionInfo())
        return false;

    // 최소 연결 수만큼 미리 생성
    for (int i = 0; i < m_minConnections; ++i)
    {
        auto conn = CreateConnection();
        if (conn)
        {
            m_connections.push(std::move(conn));
        }
        else
        {
            // 초기화 실패
            Finalize();
            return false;
        }
    }

    m_initialized = true;
    return true;
}

void NSMySQLConnectionPool::Finalize()
{
    if (!m_initialized)
        return;

    // 스레드 로컬 연결 정리
    t_localConnection.reset();

    // 모든 연결 닫기
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        while (!m_connections.empty())
        {
            m_connections.pop();
        }
    }

    m_currentConnections = 0;
    m_initialized = false;
}

std::unique_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection(int threadIndex)
{
    if (threadIndex < 0 || threadIndex >= MAX_THREADS)
    {
        threadIndex = threadIndex % MAX_THREADS;
    }

    // 스레드별 전용 커넥션 확인
    {
        std::lock_guard<std::mutex> lock(m_threadConnectionsMutex);
        auto& tc = m_threadConnections[threadIndex];
        
        if (tc.connection && tc.connection->IsConnected())
        {
            tc.lastUsed = std::chrono::steady_clock::now();
            tc.queryCount++;
            
            // 안전한 커넥션 대여: move 전에 새 커넥션으로 교체
            auto conn = std::move(tc.connection);
            tc.allocated = true;
            return conn;
        }
    }

    // 전용 커넥션이 없으면 풀에서 가져오기
    std::unique_lock<std::mutex> lock(m_mutex);

    // 사용 가능한 연결이 있을 때까지 대기
    m_cv.wait(lock, [this]() {
        return !m_connections.empty() || m_totalConnections < m_maxConnections;
    });

    // 풀에서 연결 가져오기
    if (!m_connections.empty())
    {
        auto conn = std::move(m_connections.front());
        m_connections.pop();
        m_currentConnections--;
        
        // 연결 상태 확인
        if (conn->IsConnected())
        {
            return conn;
        }
        else
        {
            // 재연결 시도
            if (conn->Reconnect())
            {
                return conn;
            }
            // 실패하면 카운트 감소
            m_totalConnections--;
        }
    }

    // 새 연결 생성
    if (m_totalConnections < m_maxConnections)
    {
        lock.unlock(); // 연결 생성 중에는 락 해제
        auto conn = CreateConnection();
        if (conn)
        {
            m_totalConnections++;
        }
        return conn;
    }

    return nullptr;
}

// 기존 API 호환성
std::unique_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection()
{
    int index = m_nextThreadIndex.fetch_add(1) % MAX_THREADS;
    return GetConnection(index);
}

void NSMySQLConnectionPool::ReturnConnection(std::unique_ptr<NSMySQLConnection> conn, int threadIndex)
{
    if (!conn)
        return;

    if (threadIndex < 0 || threadIndex >= MAX_THREADS)
    {
        threadIndex = threadIndex % MAX_THREADS;
    }

    // 연결 상태 확인
    if (!conn->IsConnected())
    {
        m_totalConnections--;
        return;
    }

    // 스레드별 전용 커넥션으로 반환
    {
        std::lock_guard<std::mutex> lock(m_threadConnectionsMutex);
        auto& tc = m_threadConnections[threadIndex];
        
        if (!tc.connection && !tc.allocated)
        {
            tc.connection = std::move(conn);
            tc.allocated = false;
            tc.lastUsed = std::chrono::steady_clock::now();
            return;
        }
    }

    // 전용 슬롯이 이미 있으면 풀에 반환
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_connections.push(std::move(conn));
        m_currentConnections++;
    }
    m_cv.notify_one();
}

// 기존 API 호환성
void NSMySQLConnectionPool::ReturnConnection(std::unique_ptr<NSMySQLConnection> conn)
{
    int index = m_nextThreadIndex.fetch_add(1) % MAX_THREADS;
    ReturnConnection(std::move(conn), index);
}

std::unique_ptr<NSMySQLConnection> NSMySQLConnectionPool::CreateConnection()
{
    auto conn = std::make_unique<NSMySQLConnection>();
    
    if (conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                      m_password.c_str(), m_database.c_str()))
    {
        m_currentConnections++;
        return conn;
    }

    return nullptr;
}

bool NSMySQLConnectionPool::LoadConnectionInfo()
{
    // 실제로는 설정 파일에서 읽어와야 함
    // 여기서는 예시로 하드코딩

    switch (m_databaseType)
    {
    case 0: // GameDB
        m_host = "localhost";
        m_user = "gameuser";
        m_password = "gamepass";
        m_database = "gamedb_" + std::to_string(m_shardId);
        break;
    case 1: // CommonDB
        m_host = "localhost";
        m_user = "commonuser";
        m_password = "commonpass";
        m_database = "commondb";
        break;
    case 2: // LogDB
        m_host = "localhost";
        m_user = "loguser";
        m_password = "logpass";
        m_database = "logdb";
        break;
    default:
        return false;
    }

    return true;
}