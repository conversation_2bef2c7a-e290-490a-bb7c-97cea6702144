#pragma once
#include <functional>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <exception>
#include <atomic>

// 심플한 Promise 구현 (레거시 호환)
template<typename T>
class DBPromise
{
public:
    using ThenCallback = std::function<void(T)>;
    using CatchCallback = std::function<void(std::exception_ptr)>;

    DBPromise() = default;
    ~DBPromise() = default;

    // 레거시 호환 Then
    DBPromise& Then(ThenCallback callback)
    {
        return Then(callback, nullptr);
    }
    
    // 게임 스레드 실행을 위한 Then (executor 지정)
    DBPromise& Then(ThenCallback callback, void* targetExecutor)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_isCompleted)
        {
            if (m_exception)
            {
                // 예외가 있고 catch 핸들러가 없으면 예외 전파
                if (!m_catchCallback)
                {
                    std::rethrow_exception(m_exception);
                }
            }
            else if (callback)
            {
                ExecuteCallback(callback, targetExecutor);
            }
        }
        else
        {
            m_thenCallback = callback;
            m_targetExecutor = targetExecutor;
        }
        
        return *this;
    }

    // 에러 처리
    DBPromise& Catch(CatchCallback callback)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_isCompleted && m_exception)
        {
            if (callback)
            {
                callback(m_exception);
            }
        }
        else
        {
            m_catchCallback = callback;
        }
        
        return *this;
    }

    // 값 설정 (비동기 작업에서 호출)
    void SetValue(T value)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_isCompleted)
        {
            throw std::runtime_error("Promise already completed");
        }
        
        m_value = std::move(value);
        m_isCompleted = true;
        
        if (m_thenCallback)
        {
            ExecuteCallback(m_thenCallback, m_targetExecutor);
        }
        
        m_cv.notify_all();
    }

    // 예외 설정 (에러 발생 시)
    void SetException(std::exception_ptr exception)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_isCompleted)
        {
            throw std::runtime_error("Promise already completed");
        }
        
        m_exception = exception;
        m_isCompleted = true;
        
        if (m_catchCallback)
        {
            m_catchCallback(m_exception);
        }
        
        m_cv.notify_all();
    }

    // 동기 대기 (블로킹)
    T Wait()
    {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_cv.wait(lock, [this] { return m_isCompleted; });
        
        if (m_exception)
        {
            std::rethrow_exception(m_exception);
        }
        
        return m_value;
    }

    // 완료 여부 확인
    bool IsCompleted() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_isCompleted;
    }

private:
    // 콜백 실행 (게임 스레드로 마샬링)
    void ExecuteCallback(ThenCallback callback, void* targetExecutor)
    {
        if (!callback)
            return;
            
        if (targetExecutor)
        {
            // 게임 스레드로 콜백 포스트
            PostToGameThread(targetExecutor, [callback, value = m_value]()
            {
                callback(value);
            });
        }
        else
        {
            // 현재 스레드에서 실행
            callback(m_value);
        }
    }
    
    // 게임 스레드로 작업 포스트 (외부에서 구현 필요)
    static void PostToGameThread(void* executor, std::function<void()> task);

private:
    mutable std::mutex m_mutex;
    std::condition_variable m_cv;
    
    T m_value{};
    std::exception_ptr m_exception;
    std::atomic<bool> m_isCompleted{false};
    
    ThenCallback m_thenCallback;
    CatchCallback m_catchCallback;
    void* m_targetExecutor = nullptr;
};