#pragma once
#include "NSDefine.h"
#include "NSSingleton.h"
#include "DBPromise.h"
#include <memory>
#include <type_traits>
#include <atomic>
#include <functional>
#include <vector>
#include <optional>
#include <source_location>

// Forward declarations
class NSDataSerializer;
class NSQueryData;
class NSStoredProcedure;
class NSMySQLConnectionPool;
class NSIOCPWorkManager;
class ThreadedWorkManager;
struct NSStorageUpdateContainer;

namespace NS
{
    struct Connection
    {
        int32 DatabaseType;
        int32 ShardId;
        
        Connection(int32 dbType = 0, int32 shard = 0) 
            : DatabaseType(dbType), ShardId(shard) {}
    };
}

// 심플하고 레거시 호환되는 데이터베이스 매니저
class NSDataBaseManager : public NS::Singleton<NSDataBaseManager>
{
    friend class NS::Singleton<NSDataBaseManager>;
    NSDataBaseManager();
    ~NSDataBaseManager();

public:
    // 초기화
    bool Initialize();
    void Finalize();
    
    // 워커 스레드 관리
    bool Start(uint32_t workThreadCnt = 0);  // 0이면 자동 계산
    void Stop();
    void ProhibitPushAccess();
    void StopAllWorkerThreadAndWait();
    
    // 연결 관리
    bool AddConnectionInfo(int32 dbType, int32 shardId, const std::string& host, int port, 
                          const std::string& dbName, const std::string& user, const std::string& password);
    NSMySQLConnectionPool* GetDBConnection(int32 dbType, int32 shardId = 0);
    void ReconnectConnection(int32 dbType, int32 shardId = 0);
    
    // 모니터링
    uint32_t GetDBThreadCount() const { return m_threadCount.value_or(0); }
    int64_t GetQueriesProcessingCount() const { return m_queriesProcessing.load(); }
    int64_t GetDBQueueSize() const;
    std::string GetConnectionPoolCountInfo() const;
    std::string GetConnectionPoolCountLog() const;
    
    // IO 카운트
    int64_t GetInputCount() const { return m_inputCount.load(); }
    int64_t GetOutputCount() const { return m_outputCount.load(); }
    void ResetIOCount() { m_inputCount = 0; m_outputCount = 0; }
    
    // 콜백 설정
    void SetAfterExecuteQuery(std::function<void(const NSQueryData&)> callback) { m_afterExecuteQuery = callback; }

    // 레거시 호환 StartQuery 오버로드들
    
    // 1. Connection과 DataSerializer 받는 버전 (기본)
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        const NS::Connection& connection,
        const NSDataSerializer& serializer)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");

        return StartQueryImpl<SP>(connection, serializer);
    }
    
    // 2. DataSerializer와 샤드키 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        NSDataSerializer& dataSerializer,
        uint64_t shardKey = 0)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        // 샤드키로 Connection 결정
        NS::Connection conn(0, shardKey % 10);  // GameDB 샤딩
        return StartQueryImpl<SP>(conn, dataSerializer);
    }
    
    // 3. 세션만 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        NSDataSerializer serializer;
        uint64_t shardKey = session->GetAID();  // AID로 샤딩
        NS::Connection conn = GetConnectionByAid(shardKey);
        
        return StartQueryImpl<SP>(conn, serializer);
    }
    
    // 4. 세션과 DataSerializer 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session,
        NSDataSerializer& dataSerializer)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        uint64_t shardKey = session->GetAID();  // AID로 샤딩
        NS::Connection conn = GetConnectionByAid(shardKey);
        
        return StartQueryImpl<SP>(conn, dataSerializer);
    }

    // Cid 기반 샤딩
    NS::Connection GetConnectionByCid(int32 cid)
    {
        return NS::Connection(0, cid % 10); // GameDB 0-9 샤딩
    }

    // Aid 기반 샤딩  
    NS::Connection GetConnectionByAid(int32 aid)
    {
        return NS::Connection(0, aid % 10); // GameDB 0-9 샤딩
    }

    // CommonDB 연결
    NS::Connection GetCommonDBConnection()
    {
        return NS::Connection(1, 0); // CommonDB
    }

    // LogDB 연결
    NS::Connection GetLogDBConnection()
    {
        return NS::Connection(2, 0); // LogDB
    }
    
    // Storage Update 관련 메서드 (레거시 호환)
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQuery(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    );

    template <typename Sp>
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQueryWithCustomProcedure(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        NSDataSerializer& dataSerializer,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    )
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
            auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
            queryData->GetQueryData() = dataSerializer;

            // CID 기반 스레드 선택
            int threadIndex = containerData->Cid % m_threadCount.value_or(1);
            
            // 작업 큐에 추가
            m_workerManager->QueueWork([=]() mutable {
                try {
                    // SP 타입의 프로시저 실행
                    auto* pool = GetConnectionPool(0, containerData->Cid % 10);
                    if (!pool) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Connection pool not found")));
                        return;
                    }
                    
                    auto connection = pool->GetConnection();
                    if (!connection) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Failed to get connection")));
                        return;
                    }
                    
                    Sp procedure;
                    auto result = procedure.Execute(connection.get(), queryData->GetQueryData());
                    queryData->SetErrorCode(result);
                    
                    // 결과 처리
                    if (pResultFunc) {
                        pResultFunc(queryData, containerData);
                    }
                    
                    promise.SetValue(queryData);
                    pool->ReturnConnection(std::move(connection));
                    
                } catch (...) {
                    promise.SetException(std::current_exception());
                }
            }, threadIndex);
        });
    }

    // 트랜잭션 지원
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQueryWithTransaction(
        const NS::Connection& connection,
        const NSDataSerializer& serializer,
        int64_t transactionId)
    {
        // 트랜잭션 ID를 사용하여 동일 연결 보장
        return StartQueryImpl<SP>(connection, serializer, transactionId);
    }

private:
    // 실제 구현
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQueryImpl(
        const NS::Connection& connection,
        const NSDataSerializer& serializer,
        int64_t transactionId = 0);

    // 연결 풀 가져오기
    NSMySQLConnectionPool* GetConnectionPool(int32 dbType, int32 shardId);
    
    // 샤드키 기반 스레드 선택
    int GetExecutorByShardKey(uint64_t shardKey) const {
        return shardKey % m_threadCount.value_or(1);
    }

private:
    // 12개 연결 풀 (GameDB 0-9 + CommonDB + LogDB)
    std::unique_ptr<NSMySQLConnectionPool> m_connectionPools[12];
    
    // 스레드별 작업 매니저 (IOCP 대체)
    std::unique_ptr<ThreadedWorkManager> m_workerManager;
    
    // 스레드 수
    std::optional<uint32_t> m_threadCount;
    
    // 통계
    std::atomic<int64_t> m_queriesProcessing{0};
    std::atomic<int64_t> m_inputCount{0};
    std::atomic<int64_t> m_outputCount{0};
    
    // 콜백
    std::function<void(const NSQueryData&)> m_afterExecuteQuery;
    
    // 상태
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_pushAccessProhibit{false};
    std::atomic<int32_t> m_pushAccessCount{0};
    
    // 시퀀스 관리 - 스레드 로컬 스토리지 사용 (락 불필요)
    struct ThreadLocalSequence {
        std::unordered_map<int64_t, int64_t> sequenceByCid;  // atomic 불필요
    };
    static thread_local ThreadLocalSequence t_localSequence;
    
public:
    // 시퀀스 관리 메서드 (최소한의 기능만)
    int64_t GetNextStorageSequence(int64_t cid);
    void OnSessionClosed(int64_t cid);
};