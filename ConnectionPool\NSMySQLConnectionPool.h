#pragma once
#include <memory>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <array>
#include <chrono>

// Forward declaration
class NSMySQLConnection;

// 심플한 MySQL 연결 풀 (스레드 로컬 캐싱 지원)
class NSMySQLConnectionPool
{
public:
    NSMySQLConnectionPool();
    ~NSMySQLConnectionPool();

    // 초기화/종료
    bool Initialize(int databaseType, int shardId);
    void Finalize();

    // 연결 획듍/반환 - 스레드 인덱스 기반
    std::unique_ptr<NSMySQLConnection> GetConnection(int threadIndex);
    void ReturnConnection(std::unique_ptr<NSMySQLConnection> conn, int threadIndex);
    
    // 기존 API 호환성 (랜덤 스레드 선택)
    std::unique_ptr<NSMySQLConnection> GetConnection();
    void ReturnConnection(std::unique_ptr<NSMySQLConnection> conn);

    // 설정
    void SetMinConnections(int count) { m_minConnections = count; }
    void SetMaxConnections(int count) { m_maxConnections = count; }

private:
    // 연결 생성
    std::unique_ptr<NSMySQLConnection> CreateConnection();
    
    // 연결 정보 로드
    bool LoadConnectionInfo();

    // 스레드별 전용 커넥션 관리
    struct ThreadConnection {
        std::unique_ptr<NSMySQLConnection> connection;
        std::chrono::steady_clock::time_point lastUsed;
        int64_t queryCount = 0;
        bool allocated = false;
    };
    
    static constexpr int MAX_THREADS = 64;
    std::array<ThreadConnection, MAX_THREADS> m_threadConnections;
    std::mutex m_threadConnectionsMutex;

private:
    // 연결 풀
    std::queue<std::unique_ptr<NSMySQLConnection>> m_connections;
    std::mutex m_mutex;
    std::condition_variable m_cv;

    // 설정
    int m_databaseType = 0;
    int m_shardId = 0;
    int m_minConnections = 5;
    int m_maxConnections = 20;
    
    // 현재 연결 수
    std::atomic<int> m_currentConnections{0};
    std::atomic<int> m_totalConnections{0};
    std::atomic<int> m_nextThreadIndex{0};
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    bool m_initialized = false;
};