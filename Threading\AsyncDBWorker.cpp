#include "../stdafx.h"
#include "AsyncDBWorker.h"
#include "../ConnectionPool/NSMySQLConnectionPool.h"
#include "../Connection/NSMySQLConnection.h"
#include <algorithm>

AsyncDBWorker::AsyncDBWorker()
{
}

AsyncDBWorker::~AsyncDBWorker()
{
    Finalize();
}

bool AsyncDBWorker::Initialize(int threadIndex, NSMySQLConnectionPool* pool)
{
    if (m_running)
        return false;

    m_threadIndex = threadIndex;
    m_pool = pool;
    m_running = true;

    // 전용 커넥션 획득
    m_connection = m_pool->GetConnection(threadIndex);
    if (!m_connection)
        return false;

    // 워커 스레드 시작
    m_workerThread = std::thread([this]() { ProcessLoop(); });

    return true;
}

void AsyncDBWorker::Finalize()
{
    if (!m_running)
        return;

    m_running = false;

    if (m_workerThread.joinable())
    {
        m_workerThread.join();
    }

    // 모든 캐시된 Statement 정리
    {
        std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
        for (auto& [procName, stmts] : m_stmtCache)
        {
            for (auto stmt : stmts)
            {
                mysql_stmt_close(stmt);
            }
        }
        m_stmtCache.clear();
    }

    // 커넥션 반환
    if (m_connection && m_pool)
    {
        m_pool->ReturnConnection(std::move(m_connection), m_threadIndex);
    }
}

void AsyncDBWorker::PostWork(WorkItem work)
{
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_cidQueues[work.cid].pending.push(std::move(work));
        m_totalPending++;
    }
}

void AsyncDBWorker::ProcessLoop()
{
    // 스레드 이름 설정
    std::string threadName = "AsyncDBWorker#" + std::to_string(m_threadIndex);
#ifdef _WIN32
    const DWORD MS_VC_EXCEPTION = 0x406D1388;
    #pragma pack(push,8)
    typedef struct tagTHREADNAME_INFO
    {
        DWORD dwType;
        LPCSTR szName;
        DWORD dwThreadID;
        DWORD dwFlags;
    } THREADNAME_INFO;
    #pragma pack(pop)

    THREADNAME_INFO info;
    info.dwType = 0x1000;
    info.szName = threadName.c_str();
    info.dwThreadID = GetCurrentThreadId();
    info.dwFlags = 0;

    __try
    {
        RaiseException(MS_VC_EXCEPTION, 0, sizeof(info)/sizeof(ULONG_PTR), (ULONG_PTR*)&info);
    }
    __except(EXCEPTION_EXECUTE_HANDLER)
    {
    }
#endif

    while (m_running)
    {
        // 1. 각 CID 큐에서 실행 가능한 작업 시작
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            for (auto& [cid, queue] : m_cidQueues)
            {
                if (!queue.isExecuting && !queue.pending.empty())
                {
                    StartNextWork(cid, queue);
                }
            }
        }

        // 2. 진행 중인 비동기 작업들 폴링
        PollActiveOperations();

        // 3. 완료된 작업 처리
        ProcessCompletedOperations();

        // CPU 사용률 조절
        if (m_activeOps.empty())
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        else
        {
            std::this_thread::yield();
        }
    }
}

void AsyncDBWorker::StartNextWork(int64_t cid, CidQueue& queue)
{
    auto work = std::move(queue.pending.front());
    queue.pending.pop();
    m_totalPending--;

    // PreparedStatement 준비
    MYSQL_STMT* stmt = GetCachedStatement(work.procName);
    if (!stmt)
    {
        // Statement 생성 실패
        work.callback(false, nullptr);
        return;
    }

    // 파라미터 바인딩
    if (work.bindParams)
    {
        work.bindParams(stmt);
    }

    // 비동기 실행 시작
    int status = 0; // 동기 실행으로 시작 (MySQL C API는 비동기를 완전히 지원하지 않음)
    
    if (mysql_stmt_execute(stmt) == 0)
    {
        // 결과 저장
        if (mysql_stmt_store_result(stmt) == 0)
        {
            // 결과를 MYSQL_RES로 변환
            MYSQL_RES* result = mysql_stmt_result_metadata(stmt);
            work.callback(true, result);
            
            if (result)
            {
                mysql_free_result(result);
            }
        }
        else
        {
            work.callback(false, nullptr);
        }
    }
    else
    {
        work.callback(false, nullptr);
    }

    // Statement 반환
    ReleaseCachedStatement(work.procName, stmt);
    
    queue.isExecuting = false;
    m_totalProcessed++;
}

void AsyncDBWorker::PollActiveOperations()
{
    // MySQL C API는 진정한 비동기를 지원하지 않으므로
    // 이 함수는 현재 구현에서는 사용되지 않음
}

void AsyncDBWorker::ProcessCompletedOperations()
{
    // MySQL C API는 진정한 비동기를 지원하지 않으므로
    // 이 함수는 현재 구현에서는 사용되지 않음
}

MYSQL_STMT* AsyncDBWorker::GetCachedStatement(const std::string& procName)
{
    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    auto& stmts = m_stmtCache[procName];
    if (!stmts.empty())
    {
        auto stmt = stmts.back();
        stmts.pop_back();
        return stmt;
    }

    // 새 Statement 생성
    if (!m_connection)
        return nullptr;

    MYSQL* mysql = m_connection->GetRawConnection();
    if (!mysql)
        return nullptr;

    MYSQL_STMT* stmt = mysql_stmt_init(mysql);
    if (!stmt)
        return nullptr;

    // 프로시저 호출 준비
    std::string query = "CALL " + procName;
    if (mysql_stmt_prepare(stmt, query.c_str(), query.length()) != 0)
    {
        mysql_stmt_close(stmt);
        return nullptr;
    }

    return stmt;
}

void AsyncDBWorker::ReleaseCachedStatement(const std::string& procName, MYSQL_STMT* stmt)
{
    if (!stmt)
        return;

    std::lock_guard<std::mutex> lock(m_stmtCacheMutex);
    
    auto& stmts = m_stmtCache[procName];
    if (stmts.size() < 10)  // 최대 10개까지 캐시
    {
        mysql_stmt_reset(stmt);
        stmts.push_back(stmt);
    }
    else
    {
        mysql_stmt_close(stmt);
    }
}