#pragma once

// 게임 서버 관련 매크로 제거 (DB 라이브러리에 불필요)

#include <stdint.h>

constexpr uint32_t ADORECORDSET_UNICODE_BUFFER_SIZE = 65536;	//ADORECORDSET UNICODE 변환버퍼 사이즈


//////////////////////////////////////////////////////////////////////////
// Define 문

#if !defined(IN)
#define IN
#endif
#if !defined(OUT)
#define OUT
#endif
#if !defined(INOUT)
#define INOUT
#endif

// 디버그 메모리 추적 및 실험적 기능 제거 (런타임에 불필요)